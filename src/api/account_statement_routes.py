import os
import traceback

import requests
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Security,
    UploadFile,
    status,
)

from api.mappers import AccountStatementResponse, FileType
from application.account_statement_service import AccountStatementService
from application.camunda_service import CamundaService
from application.draft_reconciliation_service import DraftReconciliationService
from domain.exceptions import (
    BankAccountGuidNotFoundException,
    ReconciliationFileUploadNotFoundException,
    ReconciliationFileUploadStatusException,
    ReconciliationFileUploadValidateException,
    ReconciliationNotFoundException,
)
from domain.repositories import (
    AbstractMatchRepository,
    AbstractReconciliationRepository,
    AbstractUploadRepository,
)
from infrastructure.cockpit_api_client import CockpitApiClient
from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)
from infrastructure.repositories.match_repository import MatchRepository
from infrastructure.repositories.partner_entity_repository import (
    PartnerEntityRepository,
)
from infrastructure.repositories.platform_transaction_repository import (
    PlatformTransactionRepository,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)
from infrastructure.repositories.upload_repository import UploadRepository
from infrastructure.upload_api_client import UploadApiClient
from shared.auth import Claims, Scope, Session, auth
from shared.logger import get_logger

router = APIRouter()

ALLOWED_EXTENSIONS = {".pdf", ".ofx", ".xlsx", ".xls"}

logger = get_logger()


def get_session(
    claims: Claims = Security(
        auth.verify,
        scopes=[Scope.MANAGE_RECONCILIATIONS],
    )
):
    session = Session(
        user_id=claims.sub,
        email=claims.owner.email,
        name=claims.owner.name,
        token=claims.owner.token,
    )
    return session


def get_tio_patinhas_api(session: Session = Depends(get_session)) -> UploadApiClient:
    return UploadApiClient(session=session)


def get_reconciliation_repository() -> ReconciliationRepository:
    return ReconciliationRepository()


def get_upload_repository() -> UploadRepository:
    return UploadRepository()


def get_match_repository() -> MatchRepository:
    return MatchRepository()


def get_draft_reconciliation_service(
    session: Session = Depends(get_session),
    reconciliation_repository: ReconciliationRepository = Depends(
        get_reconciliation_repository
    ),
) -> DraftReconciliationService:
    return DraftReconciliationService(
        cockpit_api_client=CockpitApiClient(),
        repository=reconciliation_repository,
        platform_transaction_repository=PlatformTransactionRepository(
            session=session,
            partner_repository=PartnerEntityRepository(),
            account_configuration_repository=AccountConfigurationRepository(),
        ),
        camunda_service=CamundaService(),
        session=session,
    )


def get_account_statement_service(
    tio_patinhas_api: UploadApiClient = Depends(get_tio_patinhas_api),
    reconciliation_repository: AbstractReconciliationRepository = Depends(
        get_reconciliation_repository
    ),
    upload_repository: AbstractUploadRepository = Depends(get_upload_repository),
    match_repository: AbstractMatchRepository = Depends(get_match_repository),
    draft_reconciliation_service: DraftReconciliationService = Depends(
        get_draft_reconciliation_service
    ),
) -> AccountStatementService:
    return AccountStatementService(
        tio_patinhas_api,
        reconciliation_repository,
        upload_repository,
        match_repository,
        draft_reconciliation_service,
    )


def validate_file_extension(filename: str) -> str:
    """Check if the file has an allowed extension"""
    ext = os.path.splitext(filename)[1].lower()
    if ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Only {', '.join(ALLOWED_EXTENSIONS)} files are supported.",  # noqa
        )

    return ext


@router.post(
    "/upload",
    status_code=status.HTTP_201_CREATED,
    tags=["Account Statements"],
)
async def upload_account_statement(
    bankaccount_guid: str = Form(...),
    reconciliation_id: str = Form(...),
    file: UploadFile = File(...),
    service: AccountStatementService = Depends(get_account_statement_service),
):
    """
    Upload a new account statement file (POST)
    """
    try:
        extension = validate_file_extension(file.filename)

        temp_file_path = "/tmp/tempfile"
        with open(temp_file_path, "wb") as buffer:
            buffer.write(await file.read())

        statement = await service.upload_statement(
            file_path=temp_file_path,
            extension=FileType.build(extension=extension[1:]).content_type,
            bankaccount_guid=bankaccount_guid,
            reconciliation_id=reconciliation_id,
        )

        os.unlink(temp_file_path)

        return AccountStatementResponse.build(
            latest_uploads_by_account=statement["latest_uploads_by_account"],
            status_bank_account_guids=statement["status_bank_account_guids"],
        )
    except FileNotFoundError as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (ReconciliationNotFoundException, BankAccountGuidNotFoundException) as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    except HTTPException as e:
        logger.error(
            "HTTPException from upload_account_method",
            extra={
                "stack_trace": traceback.format_exc(),
            },
        )
        raise e
    except Exception as e:
        logger.error(
            "Unexpected error from upload_account_method",
            extra={
                "stack_trace": traceback.format_exc(),
            },
        )
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post(
    "/upload/{upload_id}/validate",
    status_code=status.HTTP_200_OK,
    tags=["Account Statements"],
)
async def update_account_statement(
    upload_id: str,
    service: AccountStatementService = Depends(get_account_statement_service),
):
    """
    Validate sheet
    """
    try:
        statement = await service.update_statement(
            file_id=upload_id,
        )

        return AccountStatementResponse.build(
            latest_uploads_by_account=statement["latest_uploads_by_account"],
            status_bank_account_guids=statement["status_bank_account_guids"],
        )
    except ReconciliationFileUploadNotFoundException as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    except ReconciliationFileUploadStatusException as e:
        raise HTTPException(status.HTTP_409_CONFLICT, detail=str(e))
    except ReconciliationFileUploadValidateException as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=e.to_dict()
        )
    except requests.exceptions.HTTPError as e:
        logger.error(
            "Could not send request to TioPatinhas API",
            extra={
                "error": str(e),
                "stack_trace": traceback.format_exc(),
                "api_response": getattr(e.response, "text", None),
                "status_code": getattr(e.response, "status_code", None),
                "url": getattr(e.response, "url", None),
            },
        )
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    except Exception as e:
        logger.error(
            "Unexpected error from update method",
            extra={
                "stack_trace": traceback.format_exc(),
            },
        )
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get(
    "/upload/{upload_id}",
    tags=["Account Statements"],
    response_model=AccountStatementResponse,
)
async def get_account_statement_upload(
    upload_id: str,
    service: AccountStatementService = Depends(get_account_statement_service),
):
    """
    Get details of a specific account statement upload
    """
    try:
        breakpoint()
        statement = await service.get_statement(
            upload_id=upload_id,
        )

        return AccountStatementResponse.build(
            latest_uploads_by_account=statement["latest_uploads_by_account"],
            status_bank_account_guids=statement["status_bank_account_guids"],
        )
    except requests.exceptions.HTTPError as e:
        raise HTTPException(e.response.status_code, detail=e.response.json())
    except ReconciliationFileUploadNotFoundException as e:
        logger.error(
            "Not found file upload id",
            {upload_id: upload_id},
        )
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(
            "Unexpected error from get method",
            extra={
                "stack_trace": traceback.format_exc(),
            },
        )
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
