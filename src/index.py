import json

from alembic import command
from alembic import config as alembic_config
from ddtrace import tracer
from ddtrace.sampler import DatadogSampler

from application.account_service import AccountService
from application.camunda_service import CamundaMessageType
from application.match_service import ImportMatchRecords
from application.suggestion_queue_service import SuggestionQueue
from config.config import settings
from infrastructure.invoices_api_client import InvoicesApiClient
from infrastructure.orchestrate import Orchestrate
from infrastructure.repositories.account_repository import AccountRepository
from infrastructure.repositories.match_repository import MatchRepository
from infrastructure.repositories.match_s3_repository import MatchS3Repository
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)
from infrastructure.repositories.s3_repository import S3Repository
from shared.logger import get_logger

logger = get_logger()

tracer.configure(sampler=DatadogSampler(default_sample_rate=0.1))


@tracer.wrap()
def migration_handler(event, _context=False):
    """
    This function is used to run alembic migrations on the database.
    """
    config = alembic_config.Config("alembic.ini")

    downgrade_revision = event.get("downgrade_to_revision")

    if downgrade_revision:
        logger.info(f"Migrating to {downgrade_revision}")
        command.downgrade(config, revision=downgrade_revision)
        return

    logger.info("Migrating to head")
    command.upgrade(config, "head")


@tracer.wrap()
def process_match_file_handler(event, _context=False):
    """
    This function is used to process match files and save it contents in the database
    """
    logger.info("Processing match file", event)

    records = event.get("Records", [])

    for record in records:
        body = record.get("body", "{}")
        parsed_body = json.loads(body)

        s3_key = parsed_body.get("s3_key", None)

        if s3_key is None:
            logger.error("Something wrong happended. S3 Key is None", {"body": body})
            return

        match_s3_repository = MatchS3Repository(bucket_name=settings.MATCH_BUCKET)

        match_repository = MatchRepository()

        invoices_api_client = InvoicesApiClient()

        import_matches = ImportMatchRecords(
            storing=match_s3_repository,
            repository=match_repository,
            invoices_api_client=invoices_api_client,
        )

        imported_quantity = import_matches.import_match(s3_key)

        logger.info(f"[ImportMatches] {s3_key}: {imported_quantity}")


@tracer.wrap()
def process_suggestion_file_handler(event, _context=False):
    """
    This function is used to process match files from binder and
    save it contents in the database
    """
    logger.info("Processing suggestion binder file", extra={"lambda_event": event})

    logger.info(f"Suggestion event: {json.dumps(event)}")

    records = event.get("Records", [])
    count = 0

    for record in records:
        body = record.get("body", "{}")
        parsed_body = json.loads(body)

        match_repository = MatchRepository()

        invoices_api_client = InvoicesApiClient()

        import_matches = SuggestionQueue(
            repository=match_repository,
            invoices_api_client=invoices_api_client,
        )

        updated_suggestion = import_matches.import_match(parsed_body)
        length = len(updated_suggestion)
        count += length
        logger.info(f"[ImportSuggestionsBinder]: {length}")

    logger.info(
        f"Finished binder suggestion: {count} imported from {len(records)} records"
    )


@tracer.wrap()
def process_accounts_file_handler(event, _context=False):
    """
    This function is used to process chart of accounts files and save it contents in
    the database
    """
    logger.info("Processing chart of accounts files", extra={"lambda_event": event})

    records = event.get("Records", [])

    for record in records:
        body = record.get("body", "{}")
        parsed_body = json.loads(body)

        s3_key = parsed_body.get("s3_key", None)

        if s3_key is None:
            logger.error("Something wrong happended. S3 Key is None", {"body": body})
            return

        s3_repository = S3Repository(bucket_name=settings.ACCOUNT_BUCKET)

        accounts_repository = AccountRepository()

        import_accounts = AccountService(
            storing=s3_repository,
            repository=accounts_repository,
        )

        imported_quantity = import_accounts.import_accounts(s3_key)

        logger.info(f"[ImportAccounts] {s3_key}: imported {imported_quantity} accounts")


@tracer.wrap()
def camunda_handler(event, _context=False):
    """
    Handler for processing Camunda workflow messages from SQS.

    Args:
        event (dict): The SQS event containing Records
        _context: Lambda context object (unused)
    """
    logger.info("Processing Camunda message", event)
    orchestrate = Orchestrate()
    reconciliation_repository = ReconciliationRepository()

    try:
        records = event.get("Records", [])

        if not records:
            logger.info("No records to process")
            return

        for record in records:
            body = json.loads(record.get("body", "{}"))

            if body["message_type"] == CamundaMessageType.START_PROCESS.value:
                process_id = orchestrate.start_process(
                    variables=body["variables"], business_key=body["reconciliation_id"]
                )

                reconciliation = reconciliation_repository.get_reconciliation(
                    body["reconciliation_id"]
                )
                reconciliation.orchestrate_process_id = process_id
                reconciliation.save()

            elif body["message_type"] == CamundaMessageType.CORRELATE_MESSAGE.value:

                reconciliation = reconciliation_repository.get_reconciliation(
                    body["reconciliation_id"]
                )

                orchestrate.send_correlate_message(
                    message_name=body["message_name"],
                    process_instance_id=reconciliation.orchestrate_process_id,
                    business_key=body["reconciliation_id"],
                    process_variables=body["variables"],
                )

    except Exception as e:
        logger.error("Error processing message", {"error": str(e)})
        raise
