from typing import Dict, List

from sqlalchemy.exc import NoResultFound

from domain.exceptions import ReconciliationNotFoundException
from domain.models import (
    BankAccountStatus,
    JournalHeader,
    Reconciliation,
    ReconciliationStatusEnum,
    Transaction,
)
from domain.repositories import (
    AbstractMatchRepository,
    AbstractReconciliationRepository,
)


class ReconciliationRepository(AbstractReconciliationRepository):
    def __init__(self) -> None:
        super().__init__()

    def create(
        self,
        cockpit_customer_id: str,
        customer_cnpj: str,
        operator: str,
        status: ReconciliationStatusEnum,
        competence: str,
        bank_account_guids: List[str],
        status_bank_account_guids: List[str],
        transactions: List[Transaction],
        journal_headers: List[JournalHeader],
    ):
        reconciliation = Reconciliation(
            cockpit_customer_id=cockpit_customer_id,
            customer_cnpj=customer_cnpj,
            operator=operator,
            status=status,
            competence=competence,
            bank_account_guids=bank_account_guids,
            transactions=transactions,
            journal_headers=journal_headers,
            status_bank_account_guids=status_bank_account_guids,
        )
        reconciliation.save()

        return reconciliation

    def set_status(
        self, reconciliation: Reconciliation, status: ReconciliationStatusEnum
    ):
        reconciliation.status = status
        reconciliation.save()

        return reconciliation

    def set_bank_status(
        self,
        reconciliation: Reconciliation,
        status_bank_account_guids: Dict[str, BankAccountStatus],
    ):
        reconciliation.status_bank_account_guids = status_bank_account_guids
        reconciliation.save(refresh=True)

        return reconciliation

    def get_reconciliation(self, id: int):
        try:
            reconciliation = Reconciliation.get(id)

            # Convert text fields to uppercase
            if reconciliation.competence:
                reconciliation.competence = reconciliation.competence.upper()
            if reconciliation.cockpit_customer_id:
                reconciliation.cockpit_customer_id = reconciliation.cockpit_customer_id.upper()
            if reconciliation.customer_cnpj:
                reconciliation.customer_cnpj = reconciliation.customer_cnpj.upper()
            if reconciliation.operator:
                reconciliation.operator = reconciliation.operator.upper()
            if reconciliation.orchestrate_process_id:
                reconciliation.orchestrate_process_id = reconciliation.orchestrate_process_id.upper()

            return reconciliation

        except NoResultFound:
            raise ReconciliationNotFoundException(message=f"Reconciliation ID {id}")
        except Exception:
            return None

    def get_reconciliations_by_customer_and_competence(
        self, cockpit_customer_id: str, competence: str
    ):
        return Reconciliation.filter(
            Reconciliation.cockpit_customer_id == cockpit_customer_id,
            Reconciliation.competence == competence,
        )

    def check_all_journal_headers_have_suggestions(
        self,
        reconciliation: Reconciliation,
        bankaccount_guid: str,
        match_repository: AbstractMatchRepository,
    ) -> bool:
        """
        Check if all journal headers for the given bank account
        have at least one suggested invoice.
        """
        journal_headers = [
            jh
            for jh in reconciliation.journal_headers
            if jh.cockpit_account_id == bankaccount_guid
        ]

        if not journal_headers:
            return True

        transaction_ids = [jh.source_reference_id for jh in journal_headers]
        suggestions_count = match_repository.count_by_transaction_ids(transaction_ids)
        suggestions_dict = dict(suggestions_count)

        return all(tid in suggestions_dict for tid in transaction_ids)
