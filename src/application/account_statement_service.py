import enum
import json
from copy import deepcopy
from datetime import datetime, timedelta, timezone
from typing import Dict

from fastapi import HTTPException, status

from application.draft_reconciliation_service import DraftReconciliationService
from domain.exceptions import (
    BankAccountGuidNotFoundException,
    ReconciliationFileUploadStatusException,
    ReconciliationFileUploadValidateException,
    ReconciliationNotFoundException,
    StatementUploadException,
)
from domain.models import (
    BankAccountStatus,
    FileUploadStatus,
    Reconciliation,
    ReconciliationFileUpload,
)
from domain.repositories import (
    AbstractMatchRepository,
    AbstractReconciliationRepository,
    AbstractUploadRepository,
)
from infrastructure.upload_api_client import UploadApiClient
from shared.logger import get_logger

logger = get_logger()


DEFAULT_BINDER_WAIT_TIME_IN_MINUTES = 10


class UploadStatus(str, enum.Enum):
    UPLOADED = "UPLOADED"
    SHEET_READY_TO_VALIDATE = "SHEET_READY_TO_VALIDATE"
    SHEET_VALIDATION_FAILED = "SHEET_VALIDATION_FAILED"
    SHEET_VALIDATED = "SHEET_VALIDATED"
    SHEET_PROCESSED = "SHEET_PROCESSED"
    FINISHED = "FINISHED"
    ERROR = "ERROR"


class AccountStatementService:
    def __init__(
        self,
        upload_api: UploadApiClient,
        reconciliation_repository: AbstractReconciliationRepository,
        upload_repository: AbstractUploadRepository,
        match_repository: AbstractMatchRepository,
        draft_reconciliation_service: DraftReconciliationService,
    ):
        self.upload_api = upload_api
        self.reconciliation_repository = reconciliation_repository
        self.upload_repository = upload_repository
        self.match_repository = match_repository
        self.draft_reconciliation_service = draft_reconciliation_service

    async def upload_statement(
        self,
        file_path: str,
        extension: str,
        bankaccount_guid: str,
        reconciliation_id: str,
    ) -> Dict:
        """
        Upload a new account statement and update reconciliation with upload details

        Args:
            file_path: Path to the statement file
            bankaccount_guid: GUID of the bank account
            reconciliation_id: ID of the reconciliation

        Returns:
            Dictionary with upload response (id, status, sheets_url, errors)

        Raises:
            ReconciliationNotFoundException: If reconciliation not found
            BankAccountGuidNotFoundException: If bank account not in reconciliation
        """
        try:
            reconciliation: Reconciliation = (
                self.reconciliation_repository.get_reconciliation(reconciliation_id)
            )

            logger.info(f"Upload to {bankaccount_guid}")

            if reconciliation is None:
                raise ReconciliationNotFoundException

            if bankaccount_guid not in (reconciliation.bank_account_guids or []):
                raise BankAccountGuidNotFoundException

            upload_file = self.upload_api.upload_file(
                file_path=file_path,
                file_type=extension,
                bankaccount_guid=bankaccount_guid,
                competence=reconciliation.competence,
            )

            metadata = {
                "upload_response": upload_file,
                "upload_id": upload_file["id"],
                "status": FileUploadStatus.UPLOADED,
                "uploaded_at": datetime.now().isoformat(),
            }

            if upload_file.get("sheets_url"):
                metadata["sheets_url"] = upload_file["sheets_url"]

            upload_record = {
                "upload_id": upload_file["id"],
                "reconciliation_id": reconciliation_id,
                "cockpit_account_id": bankaccount_guid,
                "status": FileUploadStatus.UPLOADED,
                "file_type": extension,
                "_metadata": metadata,
                "uploaded_at": datetime.now().isoformat(),
            }

            self.upload_repository.create_upload(upload_record)

            status_bank_account_guids = deepcopy(
                reconciliation.status_bank_account_guids
            )
            status_bank_account_guids = status_bank_account_guids or {}
            status_bank_account_guids[bankaccount_guid] = (
                BankAccountStatus.EXTRACT_IMPORTING
            )

            self.reconciliation_repository.set_bank_status(
                reconciliation=reconciliation,
                status_bank_account_guids=status_bank_account_guids,
            )

            return {
                "latest_uploads_by_account": reconciliation.latest_uploads_by_account,
                "status_bank_account_guids": reconciliation.status_bank_account_guids,
            }
        except Exception as e:
            if isinstance(
                e,
                (
                    ReconciliationNotFoundException,
                    BankAccountGuidNotFoundException,
                    FileNotFoundError,
                ),
            ):
                raise

            error_metadata = {
                "error": str(e),
                "failed_at": datetime.now().isoformat(),
            }

            failed_record = {
                "reconciliation_id": reconciliation_id,
                "cockpit_account_id": bankaccount_guid,
                "status": FileUploadStatus.FAILED,
                "file_type": extension,
                "_metadata": error_metadata,
                "uploaded_at": datetime.now().isoformat(),
                "error_message": str(e),
            }

            self.upload_repository.create_upload(failed_record)

            raise StatementUploadException(message=str(e))

    async def update_statement(self, file_id: str) -> Dict:
        """
        Update an account statement (PUT)
        """
        latest_uploaded_file = self.upload_repository.get_latest_uploaded_file(
            upload_id=file_id,
        )

        if latest_uploaded_file.status in {
            FileUploadStatus.FAILED,
            FileUploadStatus.SHEET_PROCESSED,
            FileUploadStatus.CANCELLED,
        }:
            raise ReconciliationFileUploadStatusException

        reconciliation: Reconciliation = latest_uploaded_file.reconciliation
        bank_account_guid: str = latest_uploaded_file.cockpit_account_id

        validate_response = self.upload_api.validate_upload(
            file_id=file_id, competence=reconciliation.competence
        )

        if len(validate_response["invalid_data"]) > 0:
            self.upload_repository.set_status(
                latest_uploaded_file,
                FileUploadStatus.SHEET_READY_TO_VALIDATE,
                None,
                json.dumps(validate_response["invalid_data"]),
            )

            raise ReconciliationFileUploadValidateException(
                validate_response["invalid_data"]
            )

        self.upload_api.process_upload(file_id=file_id)

        self.upload_repository.set_status(
            reconciliation_file_upload=latest_uploaded_file,
            status=FileUploadStatus.PROCESSING,
        )

        status_bank_account_guids = deepcopy(reconciliation.status_bank_account_guids)
        status_bank_account_guids = status_bank_account_guids or {}
        status_bank_account_guids[bank_account_guid] = (
            BankAccountStatus.EXTRACT_PROCESSING
        )

        self.reconciliation_repository.set_bank_status(
            reconciliation=reconciliation,
            status_bank_account_guids=status_bank_account_guids,
        )

        return {
            "latest_uploads_by_account": reconciliation.latest_uploads_by_account,
            "status_bank_account_guids": reconciliation.status_bank_account_guids,
        }

    async def get_statement(self, upload_id: str) -> Dict:
        """
        Get account statement details
        """
        latest_uploaded_file: ReconciliationFileUpload = (
            self.upload_repository.get_latest_uploaded_file(
                upload_id=upload_id,
            )
        )

        reconciliation: Reconciliation = latest_uploaded_file.reconciliation
        bank_account_guid: str = latest_uploaded_file.cockpit_account_id

        logger.info(f"Getting bankaccount {bank_account_guid}")

        bank_status = deepcopy(reconciliation.status_bank_account_guids)

        if bank_status[bank_account_guid] == BankAccountStatus.EXTRACT_IMPORTED:
            return {
                "latest_uploads_by_account": reconciliation.latest_uploads_by_account,
                "status_bank_account_guids": reconciliation.status_bank_account_guids,
            }

        if bank_status[bank_account_guid] == BankAccountStatus.PENDING_SUGGESTIONS:
            self.__handle_binder_rule(
                latest_uploaded_file,
                reconciliation,
                bank_status,
                bank_account_guid,
            )
        else:
            self.__handler_file_upload_rules(
                upload_id,
                bank_status,
                bank_account_guid,
                reconciliation,
                latest_uploaded_file,
            )

        return {
            "latest_uploads_by_account": reconciliation.latest_uploads_by_account,
            "status_bank_account_guids": reconciliation.status_bank_account_guids,
        }

    def __handle_binder_rule(
        self,
        latest_uploaded_file: ReconciliationFileUpload,
        reconciliation: Reconciliation,
        bank_status: Dict[str, str],
        bank_account_guid: str,
    ):
        if latest_uploaded_file.processed_at and (
            datetime.now(timezone.utc) - latest_uploaded_file.processed_at
        ) > timedelta(minutes=DEFAULT_BINDER_WAIT_TIME_IN_MINUTES):
            logger.info(
                f"Changing to EXTRACT_IMPORTED {bank_status[bank_account_guid]} in binder rule of time"  # noqa
            )
            bank_status[bank_account_guid] = BankAccountStatus.EXTRACT_IMPORTED
            self.reconciliation_repository.set_bank_status(reconciliation, bank_status)
            logger.info(
                f"Changing to SHEET_PROCESSED {FileUploadStatus.SHEET_PROCESSED} in binder rule of time"  # noqa
            )
            self.upload_repository.set_status(
                latest_uploaded_file, FileUploadStatus.SHEET_PROCESSED
            )
        else:
            all_have_suggestions = self.reconciliation_repository.check_all_journal_headers_have_suggestions(  # noqa
                reconciliation, bank_account_guid, self.match_repository
            )

            logger.info(
                f"All have suggestions: {all_have_suggestions} for upload_id: {latest_uploaded_file.upload_id}"  # noqa
            )

            if all_have_suggestions:
                bank_status[bank_account_guid] = BankAccountStatus.EXTRACT_IMPORTED
                logger.info(
                    f"Changing to EXTRACT_IMPORTED {bank_status[bank_account_guid]} in binder rule of all have suggestions"  # noqa
                )
                self.reconciliation_repository.set_bank_status(
                    reconciliation, bank_status
                )
                logger.info(
                    f"Changing to SHEET_PROCESSED {FileUploadStatus.SHEET_PROCESSED} in binder rule of all have suggestions"  # noqa
                )
                self.upload_repository.set_status(
                    latest_uploaded_file, FileUploadStatus.SHEET_PROCESSED
                )

    def __handler_file_upload_rules(
        self,
        upload_id: str,
        bank_status: Dict[str, str],
        bankaccount_guid: str,
        reconciliation: Reconciliation,
        latest_uploaded_file: ReconciliationFileUpload,
    ):
        try:
            upload_result = self.upload_api.get_account_statement_upload(upload_id)
        except HTTPException as e:
            breakpoint()
            if e.status_code == status.HTTP_404_NOT_FOUND:
                bank_status[bankaccount_guid] = (
                    BankAccountStatus.EXTRACT_PROCESSING_ERROR
                )
                self.__handle_error_status(
                    latest_uploaded_file=latest_uploaded_file,
                    reconciliation=reconciliation,
                    file_upload_status=FileUploadStatus.FAILED,
                    bank_status=bank_status,
                    error_message=json.dumps(str(e)),
                )
            raise

        result_status = upload_result["status"]

        logger.info(f"Upload_id {upload_id} result {result_status}")
        logger.info(json.dumps(upload_result))

        if result_status == UploadStatus.FINISHED:
            bank_status[bankaccount_guid] = BankAccountStatus.PENDING_SUGGESTIONS
            self.reconciliation_repository.set_bank_status(reconciliation, bank_status)
            self.draft_reconciliation_service.sync_draft_journal(
                reconciliation, reconciliation.bank_account_guids
            )
            self.upload_repository.set_status(
                latest_uploaded_file,
                FileUploadStatus.SHEET_PROCESSED,
                datetime.now(timezone.utc).isoformat(),
            )

        if result_status == UploadStatus.SHEET_READY_TO_VALIDATE:
            bank_status[bankaccount_guid] = BankAccountStatus.EXTRACT_IMPORTING
            self.reconciliation_repository.set_bank_status(reconciliation, bank_status)
            self.upload_repository.set_status(
                latest_uploaded_file,
                FileUploadStatus.SHEET_READY_TO_VALIDATE,
                None,
                None,
                upload_result["sheets_url"],
            )

        if result_status == UploadStatus.ERROR:
            bank_status[bankaccount_guid] = BankAccountStatus.EXTRACT_PROCESSING_ERROR
            self.__handle_error_status(
                latest_uploaded_file=latest_uploaded_file,
                reconciliation=reconciliation,
                file_upload_status=FileUploadStatus.FAILED,
                bank_status=bank_status,
                error_message=json.dumps(upload_result.get("error", "")),
            )

    def __handle_error_status(
        self,
        latest_uploaded_file,
        reconciliation,
        file_upload_status,
        bank_status,
        error_message,
    ):
        self.reconciliation_repository.set_bank_status(reconciliation, bank_status)
        self.upload_repository.set_status(
            reconciliation_file_upload=latest_uploaded_file,
            status=file_upload_status,
            processed_at=None,
            error_message=error_message,
            sheets_url=None,
        )
