import base64
import io

import pandas as pd

from application.strategies.excel.excel_generation_strategy import (
    DATE_FORMAT,
    ExcelGenerationStrategy,
)
from domain.models import translate_transaction_type


class TransactionBasedExcelGeneration(ExcelGenerationStrategy):
    def generate_excel(self, reconciliation, session) -> str:
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            grouped_transactions = {}
            all_data = []

            for transaction in reconciliation.transactions:
                cockpit_account_id = transaction.cockpit_account_id or "Conta"
                transaction_type_text, _ = translate_transaction_type(transaction.type)

                base_row = {
                    "Data": transaction.date.strftime(DATE_FORMAT),
                    "Tipo": transaction_type_text,
                    "Descrição": transaction.description,
                    "CNPJ/CPF": transaction.trading_identifier,
                    "Beneficiário": transaction.trading_name,
                    "Débito": transaction.expense_account,
                    "Crédito": transaction.income_account,
                    "Valor": abs(float(transaction.amount)),
                    "Num NF": None,
                    "Observação": transaction.notes,
                }

                invoices_number = []
                if transaction.linked_invoices:
                    for linked_invoice in transaction.linked_invoices:
                        if linked_invoice.invoice_number:
                            invoices_number.append(linked_invoice.invoice_number)

                base_row = {
                    **base_row,
                    "Num NF": ",".join(invoices_number),
                }

                if cockpit_account_id not in grouped_transactions:
                    grouped_transactions[cockpit_account_id] = []
                grouped_transactions[cockpit_account_id].append(base_row)

                base_row = {
                    **base_row,
                    "Histórico": self._ExcelGenerationStrategy__get_history(
                        transaction, invoices_number
                    ),
                }

                all_data.append(base_row)

            for cockpit_account_id, data in grouped_transactions.items():
                df = pd.DataFrame(data)

                # Convert all string columns to uppercase
                for col in df.columns:
                    if df[col].dtype == 'object':  # String columns
                        df[col] = df[col].astype(str).str.upper()

                sheet_name = self._ExcelGenerationStrategy__get_bankaccount_name(
                    cockpit_account_id=cockpit_account_id, session=session
                )
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                self._ExcelGenerationStrategy__style_reconciliation_sheet(
                    writer=writer, sheet_name=sheet_name
                )

            self._ExcelGenerationStrategy__generate_common_excel(all_data, writer)

        output.seek(0)
        return base64.b64encode(output.read()).decode("utf-8")
