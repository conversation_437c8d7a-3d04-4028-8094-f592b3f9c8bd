import requests
from fastapi import HTTPException


def raise_http_error(response):
    breakpoint()
    try:
        error_details = response.json()

        if error_details.get("status") and error_details["status"] == "ERROR":
            raise HTTPException(response.status_code, detail=error_details["error"])

        if not response.ok:
            try:
                error_msg = error_details["detail"]
            except (<PERSON><PERSON><PERSON><PERSON>, TypeError, ValueError):
                error_msg = response.text
            raise HTTPException(response.status_code, detail=str(error_msg))

    except requests.exceptions.JSONDecodeError:
        if not response.ok:
            raise HTTPException(response.status_code, detail=str(response.text))

    response.raise_for_status()
