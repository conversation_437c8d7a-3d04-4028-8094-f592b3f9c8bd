import json
import os
from unittest.mock import MagicMock, patch

import pytest

from domain.models import Account
from index import process_accounts_file_handler

os.environ["APP_ENV"] = "test"


@pytest.fixture
def mock_s3_repository():
    with patch("index.S3Repository") as mock:
        yield mock


@pytest.fixture
def mock_match_repository():
    with patch("index.AccountRepository") as mock:
        yield mock


@pytest.fixture
def mock_import_accounts_records():
    with patch("index.AccountService") as mock:
        yield mock


@patch("index.logger")
def test_process_account_file_handler_valid_event(
    mock_logger,
    mock_s3_repository,
    mock_match_repository,
    mock_import_accounts_records,
):
    event = {"Records": [{"body": json.dumps({"s3_key": "valid/s3/path"})}]}
    mock_import_instance = MagicMock()
    mock_import_instance.import_accounts.return_value = 5
    mock_import_accounts_records.return_value = mock_import_instance

    process_accounts_file_handler(event, None)

    mock_logger.info.assert_called_with(
        "[ImportAccounts] valid/s3/path: imported 5 accounts"
    )
    mock_import_instance.import_accounts.assert_called_once_with("valid/s3/path")


@patch("index.logger")
def test_process_account_file_handler_no_records(mock_logger):
    event = {"Records": []}

    process_accounts_file_handler(event, None)

    mock_logger.info.assert_called_once_with(
        "Processing chart of accounts files", extra={"lambda_event": {"Records": []}}
    )


@patch("index.logger")
def test_process_account_file_handler_no_s3_key(
    mock_logger,
    mock_s3_repository,
    mock_match_repository,
    mock_import_accounts_records,
):
    event = {"Records": [{"body": json.dumps({})}]}

    process_accounts_file_handler(event, None)

    mock_logger.error.assert_called_once_with(
        "Something wrong happended. S3 Key is None", {"body": "{}"}
    )


@patch("index.logger")
def test_process_account_file_handler_import_fail(
    mock_logger,
    mock_s3_repository,
    mock_match_repository,
    mock_import_accounts_records,
):
    event = {"Records": [{"body": json.dumps({"s3_key": "valid/s3/path"})}]}
    mock_import_instance = MagicMock()
    mock_import_instance.import_accounts.side_effect = Exception("Import failed")
    mock_import_accounts_records.return_value = mock_import_instance

    with pytest.raises(Exception) as excinfo:
        process_accounts_file_handler(event, None)

    assert str(excinfo.value) == "Import failed"


@patch("index.logger")
def test_process_account_file_handler_import(mock_logger, mock_s3_repository, db_test):
    s3_file_content = {
        "cnpj": "**************",
        "accounts": [
            {
                "cnpj": "***********",
                "parent_code": 102,
                "description": "Conta de Recebíveis",
                "classification": "1.01.01.00001",
                "level": 2,
                "code": 10,
                "status": "ACTIVE",
                "type": "ANALYTIC",
                "nature": "DEBIT",
                "updated_at": "2025-03-13T14:30:00Z",
            }
        ],
    }

    event = {"Records": [{"body": json.dumps({"s3_key": "valid/s3/path"})}]}

    mock_s3_repository.return_value.get_object.return_value = json.dumps(
        s3_file_content
    ).encode("utf-8")

    process_accounts_file_handler(event, None)

    accounts = Account.all()

    assert len(accounts) == 1
    assert accounts[0].cnpj == "**************"


@patch("index.logger")
def test_process_account_file_handler_import_with_existing_records(
    mock_logger, mock_s3_repository, db_test
):
    s3_file_content = {
        "cnpj": "**************",
        "accounts": [
            {
                "cnpj": "***********",
                "parent_code": 102,
                "description": "Recebíveis",
                "classification": "1.01.01.00001",
                "level": 2,
                "code": 10,
                "status": "ACTIVE",
                "account_type": "ANALYTIC",
                "nature": "DEBIT",
                "updated_at": "2025-03-13T14:30:00Z",
            }
        ],
    }

    Account(
        cnpj="**************",
        parent_code=102,
        description="Conta de Recebíveis",
        classification="1.01.01.00001",
        level=2,
        short_code=10,
        status="ACTIVE",
        account_type="ANALYTIC",
        operation_type="DEBIT",
    ).save()

    event = {"Records": [{"body": json.dumps({"s3_key": "valid/s3/path"})}]}

    mock_s3_repository.return_value.get_object.return_value = json.dumps(
        s3_file_content
    ).encode("utf-8")

    process_accounts_file_handler(event, None)

    accounts = Account.all()

    assert len(accounts) == 1
    assert accounts[0].cnpj == "**************"
    assert accounts[0].description == "Recebíveis"
