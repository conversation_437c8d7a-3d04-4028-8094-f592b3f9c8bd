import aws_cdk.aws_sns as Sns
import aws_cdk.aws_sns_subscriptions as SnsSubscriptions
import aws_cdk.aws_sqs as aws_sqs
from aws_cdk import Duration
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_elasticloadbalancingv2 as elbv2
from aws_cdk import aws_iam as iam
from aws_cdk import aws_route53 as route53
from aws_cdk import aws_route53_targets as targets
from aws_cdk import aws_s3 as s3
from aws_cdk.aws_ecr_assets import DockerImageAsset, Platform
from aws_cdk.aws_ecs import Cluster, ContainerImage, FargateService
from bhub_cdk.dns import PublicHostedZone
from bhub_cdk.stack import ApplicationNestedStack
from bhub_cdk.vpc import PrivateSubnets, Vpc
from constructs import Construct

from infrastructure.database import RdsPostgres
from infrastructure.ecs import DatadogLogSource, ECSTask
from infrastructure.function import <PERSON>da
from infrastructure.sqs import Sqs


class ApplicationStack(ApplicationNestedStack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        environment: dict,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        camunda_queue = Sqs(self, "CamundaQueue", visibility_timeout=180, fifo=True)

        database = RdsPostgres(
            self,
            "ConciliadorBackDatabase",
            database_name="conciliador_back",
            environment=self.node.try_get_context("deploy_env") or "staging",
        )

        environment.update(
            {
                "CAMUNDA_QUEUE_URL": camunda_queue.queue.queue_url,
            }
        )

        secret_settings = {
            "DATABASE_SECRET_ARN": database._instance.secret,
        }

        self.cluster = Cluster(self, "FastApiCluster", vpc=Vpc.shared(self))
        self.subnet_selection = PrivateSubnets.select_for(self, self.BUSINESS_UNIT)

        api_service = self.create_ecs_service(
            "ApiService",
            dd_service="conciliador-back",
            dockerfile_name="Dockerfile.api",
            env_vars={
                **environment,
                "DATABASE_SECRET_ARN": database._instance.secret.secret_arn,
            },
        )

        api_service_role = api_service.task_definition.task_role
        api_service_role.add_to_policy(
            iam.PolicyStatement(
                actions=["secretsmanager:GetSecretValue"],
                resources=[database._instance.secret.secret_arn],
            )
        )

        migration_lambda = Lambda(
            self,
            "MigrationLambda",
            environment=environment,
            secrets=secret_settings,
            handler="index.migration_handler",
            memory_size=256,
            timeout=Duration.minutes(3),
        )

        camunda_lambda = Lambda(
            self,
            "CamundaLambda",
            environment=environment,
            secrets=secret_settings,
            handler="index.camunda_handler",
            memory_size=256,
            timeout=Duration.minutes(3),
        )

        camunda_queue.grant_produce(api_service_role)
        camunda_queue.grant_consume(camunda_lambda)
        camunda_lambda.add_sqs_trigger(camunda_queue.queue)
        camunda_lambda.connection_allow_to_default_port(database._instance)

        process_match_file_lambda = Lambda(
            self,
            "ProcessMatchFile",
            environment=environment,
            secrets=secret_settings,
            handler="index.process_match_file_handler",
            memory_size=256,
            timeout=Duration.minutes(15),
        )

        process_accounts_file_lambda = Lambda(
            self,
            "ProcessAccountsFile",
            environment=environment,
            secrets=secret_settings,
            handler="index.process_accounts_file_handler",
            memory_size=512,
            timeout=Duration.minutes(15),
        )

        process_suggestion_lambda = Lambda(
            self,
            "ProcessSuggestionFile",
            environment=environment,
            secrets=secret_settings,
            handler="index.process_suggestion_file_handler",
            memory_size=256,
            timeout=Duration.minutes(10),
        )

        match_bucket = s3.Bucket.from_bucket_name(
            self,
            "MatchBucket",
            environment.get("MATCH_BUCKET"),
        )

        match_bucket.grant_read_write(process_match_file_lambda)
        match_bucket.grant_put_acl(process_match_file_lambda)

        account_bucket = s3.Bucket.from_bucket_name(
            self,
            "AccountBucket",
            environment.get("ACCOUNT_BUCKET"),
        )

        account_bucket.grant_read_write(process_accounts_file_lambda)
        account_bucket.grant_put_acl(process_accounts_file_lambda)

        process_accounts_file_lambda.connection_allow_to_default_port(
            database._instance
        )
        process_suggestion_lambda.connection_allow_to_default_port(database._instance)
        process_match_file_lambda.connection_allow_to_default_port(database._instance)
        migration_lambda.connection_allow_to_default_port(database._instance)
        database._instance.connections.allow_default_port_from(api_service)

        external_queue = Sqs(self, "ExternalQueue", visibility_timeout=900)

        external_topic = Sns.Topic(
            self,
            "ExternalTopic",
        )
        external_topic.add_subscription(
            SnsSubscriptions.SqsSubscription(
                queue=external_queue.queue, raw_message_delivery=True
            )
        )

        external_topic.add_to_resource_policy(
            statement=iam.PolicyStatement(
                actions=["SNS:Publish"],
                resources=[external_topic.topic_arn],
                principals=[
                    iam.ArnPrincipal(
                        "arn:aws:iam::************:role/bhub-lakehouse-gcog12-databricks-assumable-role"  # noqa
                    )
                ],
            )
        )
        process_match_file_lambda.add_sqs_trigger(
            external_queue.queue, batch_size=1, max_concurrency=10
        )

        suggestion_queue = aws_sqs.Queue.from_queue_arn(
            self,
            "SuggestionQueue",
            queue_arn=environment.get("BINDER_ARN"),
        )

        suggestion_queue.grant_consume_messages(process_suggestion_lambda)
        suggestion_queue.grant_send_messages(process_suggestion_lambda)

        suggestion_queue.grant(
            process_suggestion_lambda,
            "sqs:ReceiveMessage",
            "sqs:ChangeMessageVisibility",
            "sqs:GetQueueUrl",
            "sqs:DeleteMessage",
            "sqs:GetQueueAttributes",
            "sqs:SendMessage",
        )

        process_suggestion_lambda.add_sqs_trigger(
            suggestion_queue, batch_size=1, max_concurrency=10
        )

        external_accounts_queue = Sqs(
            self, "ExternalAccountsQueue", visibility_timeout=900
        )

        external_account_topic = Sns.Topic(
            self,
            "ExternalAccountTopic",
        )
        external_account_topic.add_subscription(
            SnsSubscriptions.SqsSubscription(
                queue=external_accounts_queue.queue, raw_message_delivery=True
            )
        )

        external_account_topic.add_to_resource_policy(
            statement=iam.PolicyStatement(
                actions=["SNS:Publish"],
                resources=[external_account_topic.topic_arn],
                principals=[
                    iam.ArnPrincipal(
                        "arn:aws:iam::************:role/bhub-lakehouse-gcog12-databricks-assumable-role"  # noqa
                    )
                ],
            )
        )
        process_accounts_file_lambda.add_sqs_trigger(
            external_accounts_queue.queue, batch_size=1, max_concurrency=10
        )

        database._instance.connections.allow_default_port_from(
            other=ec2.Peer.ipv4("10.0.0.0/16"),
            description="Allow connection from Datalake",
        )

        public_hosted_zone = PublicHostedZone(
            self, "PublicHostedZone", subdomain="conciliador-api"
        )

        load_balancer = elbv2.ApplicationLoadBalancer(
            self,
            "FastApiALB",
            vpc=self.cluster.vpc,
            internet_facing=True,
        )

        load_balancer.add_listener(
            "Listener",
            port=80,
            open=True,
            default_action=elbv2.ListenerAction.redirect(
                protocol="HTTPS",
                port="443",
                permanent=True,
            ),
        )

        https_listener = load_balancer.add_listener(
            "HttpsListener",
            port=443,
            certificates=[public_hosted_zone.certificate],
            open=True,
        )

        https_listener.add_targets(
            "ApiServiceTarget",
            port=80,
            targets=[api_service],
            health_check=elbv2.HealthCheck(
                path="/healthz/",
                interval=Duration.seconds(30),
                timeout=Duration.seconds(5),
                healthy_threshold_count=2,
                unhealthy_threshold_count=2,
            ),
        )

        route53.ARecord(
            self,
            "ApiRecord",
            zone=public_hosted_zone.sub_zone,
            target=route53.RecordTarget.from_alias(
                targets.LoadBalancerTarget(load_balancer)
            ),
        )

    def create_ecs_service(
        self,
        id: str,
        dd_service: str,
        dockerfile_name: str,
        env_vars: dict,
        secrets: dict = None,
    ) -> FargateService:
        image_asset = DockerImageAsset(
            self,
            f"{id}Image",
            directory=".",
            file=dockerfile_name,
            platform=Platform.LINUX_AMD64,
        )
        image = ContainerImage.from_docker_image_asset(image_asset)
        ecs_task = ECSTask(
            self,
            f"{id}Task",
            memory_limit_mib=1024,
            cpu=512,
            image=image,
            environment=env_vars,
            secrets=secrets,
            dd_service=dd_service,
            dd_source=DatadogLogSource.PYTHON,
            dd_version="1.0.0",
            container_port=8000,
        )

        service = FargateService(
            self,
            f"{id}Service",
            cluster=self.cluster,
            task_definition=ecs_task.task_definition,
            service_name=id,
            vpc_subnets=self.subnet_selection,
            desired_count=2,
        )

        return service
