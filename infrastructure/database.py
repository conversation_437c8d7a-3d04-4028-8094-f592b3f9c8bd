import aws_cdk.aws_ec2 as ec2
import aws_cdk.aws_rds as rds
from aws_cdk import Duration, RemovalPolicy, Stack
from aws_cdk import aws_secretsmanager as secretsmanager
from bhub_cdk.common import BusinessUnit
from bhub_cdk.vpc import PrivateSubnets, Vpc
from constructs import Construct

BACKUP_SEVEN_DAYS = 7
BACKUP_ONE_DAY = 1


class RdsPostgres(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        database_name: str,
        environment: str = "staging",
        business_unit: BusinessUnit = None,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)
        stack = Stack.of(scope)
        vpc = Vpc.shared(scope)
        stack_business_unit = getattr(stack, "BUSINESS_UNIT", None)
        vpc_subnets = None

        if stack_business_unit:
            vpc_subnets = PrivateSubnets.select_for(scope, stack_business_unit)
        elif business_unit:
            vpc_subnets = PrivateSubnets.select_for(scope, business_unit)
        else:
            raise ValueError(
                "Parameter `business_unit` is mandatory without specifying"
                " `BUSINESS_UNIT` in the parent stack"
            )

        is_prod = environment.lower() == "prod"

        self._instance = rds.DatabaseInstance(
            self,
            construct_id,
            engine=rds.DatabaseInstanceEngine.postgres(
                version=rds.PostgresEngineVersion.VER_16
            ),
            database_name=database_name,
            vpc=vpc,
            vpc_subnets=vpc_subnets,
            instance_type=ec2.InstanceType.of(
                ec2.InstanceClass.T3, ec2.InstanceSize.SMALL
            ),
            backup_retention=Duration.days(
                BACKUP_SEVEN_DAYS if is_prod else BACKUP_ONE_DAY
            ),
            enable_performance_insights=is_prod,
            deletion_protection=is_prod,
            storage_encrypted=True,
            preferred_backup_window="03:00-04:00",
            auto_minor_version_upgrade=True,
            copy_tags_to_snapshot=True,
            removal_policy=RemovalPolicy.RETAIN,
            storage_encryption_key=secretsmanager.Secret(self, "DbEncryptionKey"),
        )
